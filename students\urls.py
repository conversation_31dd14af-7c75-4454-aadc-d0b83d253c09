from django.urls import path
from . import views

app_name = 'students'

urlpatterns = [
    # ============================================================================
    # ENDPOINT 1: Student Management (CRUD Operations)
    # ============================================================================
    path('students/', views.StudentListCreateView.as_view(), name='student-list-create'),
    path('students/<int:pk>/', views.StudentDetailView.as_view(), name='student-detail'),
    
    # ============================================================================
    # ENDPOINT 2: Course Management (CRUD Operations)
    # ============================================================================
    path('courses/', views.CourseListCreateView.as_view(), name='course-list-create'),
    path('courses/<int:pk>/', views.CourseDetailView.as_view(), name='course-detail'),
    
    # ============================================================================
    # ENDPOINT 3: Enrollment Management
    # ============================================================================
    path('enrollments/', views.EnrollmentListCreateView.as_view(), name='enrollment-list-create'),
    path('enrollments/<int:pk>/', views.EnrollmentDetailView.as_view(), name='enrollment-detail'),
    
    # ============================================================================
    # ENDPOINT 4: Grade Management
    # ============================================================================
    path('grades/', views.GradeListCreateView.as_view(), name='grade-list-create'),
    path('grades/<int:pk>/', views.GradeDetailView.as_view(), name='grade-detail'),
    
    # ============================================================================
    # ENDPOINT 5: Attendance Management
    # ============================================================================
    path('attendance/', views.AttendanceListCreateView.as_view(), name='attendance-list-create'),
    path('attendance/<int:pk>/', views.AttendanceDetailView.as_view(), name='attendance-detail'),
    
    # ============================================================================
    # ENDPOINT 6: Department Management
    # ============================================================================
    path('departments/', views.DepartmentListCreateView.as_view(), name='department-list-create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department-detail'),
    
    # ============================================================================
    # ENDPOINT 7: Teacher Management
    # ============================================================================
    path('teachers/', views.TeacherListCreateView.as_view(), name='teacher-list-create'),
    path('teachers/<int:pk>/', views.TeacherDetailView.as_view(), name='teacher-detail'),
    
    # ============================================================================
    # ENDPOINT 8: Analytics & Dashboard Data
    # ============================================================================
    path('analytics/dashboard/', views.dashboard_analytics, name='dashboard-analytics'),
    
    # ============================================================================
    # ENDPOINT 9: Bulk Operations & Advanced Queries
    # ============================================================================
    path('attendance/bulk-mark/', views.bulk_attendance_mark, name='bulk-attendance-mark'),
    path('students/<int:student_id>/performance-report/', views.student_performance_report, name='student-performance-report'),
]

# API Documentation URLs (for easy reference)
"""
API Endpoints Summary:

1. Student Management:
   - GET/POST /api/students/ - List all students / Create new student
   - GET/PUT/PATCH/DELETE /api/students/{id}/ - Student details and operations

2. Course Management:
   - GET/POST /api/courses/ - List all courses / Create new course
   - GET/PUT/PATCH/DELETE /api/courses/{id}/ - Course details and operations

3. Enrollment Management:
   - GET/POST /api/enrollments/ - List enrollments / Enroll student in course
   - GET/PUT/PATCH/DELETE /api/enrollments/{id}/ - Enrollment details and operations

4. Grade Management:
   - GET/POST /api/grades/ - List grades / Add new grade
   - GET/PUT/PATCH/DELETE /api/grades/{id}/ - Grade details and operations

5. Attendance Management:
   - GET/POST /api/attendance/ - List attendance / Mark attendance
   - GET/PUT/PATCH/DELETE /api/attendance/{id}/ - Attendance details and operations

6. Department Management:
   - GET/POST /api/departments/ - List departments / Create department
   - GET/PUT/PATCH/DELETE /api/departments/{id}/ - Department details and operations

7. Teacher Management:
   - GET/POST /api/teachers/ - List teachers / Create teacher
   - GET/PUT/PATCH/DELETE /api/teachers/{id}/ - Teacher details and operations

8. Analytics & Dashboard:
   - GET /api/analytics/dashboard/ - Get comprehensive dashboard analytics

9. Bulk Operations & Reports:
   - POST /api/attendance/bulk-mark/ - Mark attendance for multiple students
   - GET /api/students/{id}/performance-report/ - Get detailed student performance report

Features:
- All endpoints support filtering, searching, and pagination
- Token-based authentication required
- Comprehensive error handling and validation
- Optimized database queries with select_related and prefetch_related
- Soft delete for students, teachers, and courses
- Automatic calculation of grades, attendance percentages, and statistics
"""
