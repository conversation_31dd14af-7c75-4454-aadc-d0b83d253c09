#!/usr/bin/env bash
# exit on error
set -o errexit

# Install dependencies
pip install -r requirements.txt

# Collect static files
python manage.py collectstatic --no-input

# Run migrations
python manage.py migrate

# Create superuser (optional - for demo purposes)
echo "from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin123') if not User.objects.filter(username='admin').exists() else None" | python manage.py shell

# Create sample data (optional - comment out if not needed)
python manage.py create_sample_data --clear || echo "Sample data creation failed, continuing..."
