# Student Management System API Documentation

## Overview
This is a comprehensive Student Management System built with Django and Django REST Framework. The system provides 9 main API endpoints for managing students, courses, teachers, departments, enrollments, grades, and attendance records.

## Authentication
The API uses Token-based authentication. To access protected endpoints, include the token in the Authorization header:
```
Authorization: Token your_token_here
```

To obtain a token, POST to `/api-token-auth/` with username and password.

## Base URL
All API endpoints are prefixed with `/api/`

## API Endpoints

### 1. Student Management
**Base URL:** `/api/students/`

#### List/Create Students
- **GET** `/api/students/` - List all students with filtering and pagination
- **POST** `/api/students/` - Create a new student

**Query Parameters:**
- `search` - Search by name, email, or student ID
- `department` - Filter by department ID
- `year_of_study` - Filter by year (1-4)
- `is_active` - Filter by active status (true/false)
- `gender` - Filter by gender (M/F/O)
- `ordering` - Order by fields (e.g., `last_name`, `-admission_date`)

#### Student Details
- **GET** `/api/students/{id}/` - Retrieve student details
- **PUT/PATCH** `/api/students/{id}/` - Update student information
- **DELETE** `/api/students/{id}/` - Soft delete student (sets is_active=False)

### 2. Course Management
**Base URL:** `/api/courses/`

#### List/Create Courses
- **GET** `/api/courses/` - List all courses
- **POST** `/api/courses/` - Create a new course

**Query Parameters:**
- `search` - Search by course name or code
- `department` - Filter by department ID
- `teacher` - Filter by teacher ID
- `semester` - Filter by semester (FALL/SPRING/SUMMER)
- `year` - Filter by year
- `is_active` - Filter by active status

#### Course Details
- **GET** `/api/courses/{id}/` - Retrieve course details
- **PUT/PATCH** `/api/courses/{id}/` - Update course information
- **DELETE** `/api/courses/{id}/` - Soft delete course

### 3. Enrollment Management
**Base URL:** `/api/enrollments/`

#### List/Create Enrollments
- **GET** `/api/enrollments/` - List all enrollments
- **POST** `/api/enrollments/` - Enroll a student in a course

**Query Parameters:**
- `student` - Filter by student ID
- `course` - Filter by course ID
- `status` - Filter by status (ENROLLED/DROPPED/COMPLETED/FAILED)
- `is_active` - Filter by active status

#### Enrollment Details
- **GET** `/api/enrollments/{id}/` - Retrieve enrollment details
- **PUT/PATCH** `/api/enrollments/{id}/` - Update enrollment status
- **DELETE** `/api/enrollments/{id}/` - Remove enrollment

### 4. Grade Management
**Base URL:** `/api/grades/`

#### List/Create Grades
- **GET** `/api/grades/` - List all grades
- **POST** `/api/grades/` - Add a new grade

**Query Parameters:**
- `enrollment` - Filter by enrollment ID
- `assessment_type` - Filter by type (QUIZ/ASSIGNMENT/MIDTERM/FINAL/PROJECT/PRESENTATION)
- `letter_grade` - Filter by letter grade (A/B/C/D/F)
- `start_date` - Filter grades from this date
- `end_date` - Filter grades until this date

#### Grade Details
- **GET** `/api/grades/{id}/` - Retrieve grade details
- **PUT/PATCH** `/api/grades/{id}/` - Update grade
- **DELETE** `/api/grades/{id}/` - Remove grade

### 5. Attendance Management
**Base URL:** `/api/attendance/`

#### List/Create Attendance
- **GET** `/api/attendance/` - List attendance records
- **POST** `/api/attendance/` - Mark attendance

**Query Parameters:**
- `enrollment` - Filter by enrollment ID
- `status` - Filter by status (PRESENT/ABSENT/LATE/EXCUSED)
- `start_date` - Filter from this date
- `end_date` - Filter until this date

#### Attendance Details
- **GET** `/api/attendance/{id}/` - Retrieve attendance record
- **PUT/PATCH** `/api/attendance/{id}/` - Update attendance
- **DELETE** `/api/attendance/{id}/` - Remove attendance record

### 6. Department Management
**Base URL:** `/api/departments/`

#### List/Create Departments
- **GET** `/api/departments/` - List all departments
- **POST** `/api/departments/` - Create a new department

#### Department Details
- **GET** `/api/departments/{id}/` - Retrieve department details
- **PUT/PATCH** `/api/departments/{id}/` - Update department
- **DELETE** `/api/departments/{id}/` - Delete department (if no related data)

### 7. Teacher Management
**Base URL:** `/api/teachers/`

#### List/Create Teachers
- **GET** `/api/teachers/` - List all teachers
- **POST** `/api/teachers/` - Create a new teacher

**Query Parameters:**
- `department` - Filter by department ID
- `qualification` - Filter by qualification
- `is_active` - Filter by active status

#### Teacher Details
- **GET** `/api/teachers/{id}/` - Retrieve teacher details
- **PUT/PATCH** `/api/teachers/{id}/` - Update teacher information
- **DELETE** `/api/teachers/{id}/` - Soft delete teacher

### 8. Analytics & Dashboard
**Base URL:** `/api/analytics/`

#### Dashboard Analytics
- **GET** `/api/analytics/dashboard/` - Get comprehensive dashboard data

**Response includes:**
- Overview statistics (total students, courses, teachers, departments)
- Students by department and year
- Course enrollment statistics
- Grade distribution
- Average attendance percentage

### 9. Bulk Operations & Reports
**Base URL:** `/api/`

#### Bulk Attendance Marking
- **POST** `/api/attendance/bulk-mark/` - Mark attendance for multiple students

**Request Body:**
```json
{
  "course_id": 1,
  "date": "2024-01-15",
  "attendance_records": [
    {"student_id": 1, "status": "PRESENT"},
    {"student_id": 2, "status": "ABSENT"}
  ],
  "marked_by": 1
}
```

#### Student Performance Report
- **GET** `/api/students/{id}/performance-report/` - Get detailed student performance

**Response includes:**
- Student basic information
- All enrollments with grades and attendance
- Overall GPA and attendance percentage
- Performance statistics

## Error Handling
The API returns appropriate HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

Error responses include detailed error messages:
```json
{
  "error": "Validation failed",
  "details": {
    "email": ["This field must be unique."]
  }
}
```

## Pagination
List endpoints support pagination with the following parameters:
- `page` - Page number
- `page_size` - Number of items per page (default: 20)

## Data Validation
The API includes comprehensive validation:
- Email uniqueness for students
- Student ID uniqueness
- Course code uniqueness per semester/year
- Enrollment constraints (no duplicate enrollments, course capacity)
- Grade validation (points earned ≤ points possible)
- Attendance uniqueness per student per date

## Features
- **Optimized Queries**: Uses `select_related` and `prefetch_related` for efficient database queries
- **Soft Delete**: Students, teachers, and courses are soft-deleted (is_active=False)
- **Automatic Calculations**: Grades, attendance percentages, and statistics are calculated automatically
- **Comprehensive Filtering**: All list endpoints support filtering, searching, and ordering
- **Token Authentication**: Secure API access with token-based authentication
- **Admin Interface**: Full Django admin interface for data management
- **Sample Data**: Management command to create sample data for testing
