{% extends 'base.html' %}

{% block title %}Enrollments - EduManage{% endblock %}

{% block page_title_main %}Enrollments{% endblock %}
{% block page_description %}Manage student course enrollments{% endblock %}

{% block page_actions %}
<a href="/admin/students/enrollment/add/" class="btn btn-primary">
    <i class="fas fa-plus me-2"></i>New Enrollment
</a>
{% endblock %}

{% block content %}
<!-- Filter Bar -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body p-3">
                <form method="get" class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <select class="form-select" name="status">
                            <option value="">All Status</option>
                            <option value="ENROLLED" {% if request.GET.status == 'ENROLLED' %}selected{% endif %}>Enrolled</option>
                            <option value="COMPLETED" {% if request.GET.status == 'COMPLETED' %}selected{% endif %}>Completed</option>
                            <option value="DROPPED" {% if request.GET.status == 'DROPPED' %}selected{% endif %}>Dropped</option>
                            <option value="FAILED" {% if request.GET.status == 'FAILED' %}selected{% endif %}>Failed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="course">
                            <option value="">All Courses</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}" {% if request.GET.course == course.id|stringformat:"s" %}selected{% endif %}>
                                {{ course.course_code }} - {{ course.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input class="form-control" type="search" name="search" placeholder="Search student or course..." value="{{ request.GET.search }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i>
                            </button>
                            <a href="{% url 'students:enrollment_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Enrollments Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Student</th>
                        <th>Course</th>
                        <th>Enrollment Date</th>
                        <th>Status</th>
                        <th>Final Grade</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for enrollment in enrollments %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <span class="text-white small">{{ enrollment.student.first_name.0 }}{{ enrollment.student.last_name.0 }}</span>
                                </div>
                                <div>
                                    <div class="fw-bold">{{ enrollment.student.full_name }}</div>
                                    <small class="text-muted">{{ enrollment.student.student_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <div class="fw-bold">{{ enrollment.course.course_code }}</div>
                                <small class="text-muted">{{ enrollment.course.name }}</small>
                            </div>
                        </td>
                        <td>{{ enrollment.enrollment_date|date:"M d, Y" }}</td>
                        <td>
                            {% if enrollment.status == 'ENROLLED' %}
                                <span class="badge bg-primary-subtle text-primary">{{ enrollment.status }}</span>
                            {% elif enrollment.status == 'COMPLETED' %}
                                <span class="badge bg-success-subtle text-success">{{ enrollment.status }}</span>
                            {% elif enrollment.status == 'DROPPED' %}
                                <span class="badge bg-secondary-subtle text-secondary">{{ enrollment.status }}</span>
                            {% else %}
                                <span class="badge bg-danger-subtle text-danger">{{ enrollment.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if enrollment.final_grade %}
                                <span class="badge bg-info-subtle text-info">{{ enrollment.final_grade }}</span>
                            {% else %}
                                <small class="text-muted">-</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/admin/students/enrollment/{{ enrollment.id }}/change/" class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="#" class="btn btn-outline-info" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No enrollments found.</p>
                            <a href="/admin/students/enrollment/add/" class="btn btn-primary">Add First Enrollment</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ enrollments|length }}</h4>
                <small class="text-muted">Total Shown</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">Active</h4>
                <small class="text-muted">Enrollments</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">Completed</h4>
                <small class="text-muted">This Semester</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">Pending</h4>
                <small class="text-muted">Reviews</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
