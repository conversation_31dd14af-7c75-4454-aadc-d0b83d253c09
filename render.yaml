databases:
  - name: student-management-db
    databaseName: student_management
    user: student_management_user

services:
  - type: web
    name: student-management-system
    runtime: python3
    buildCommand: "./build.sh"
    startCommand: "gunicorn student_management.wsgi:application"
    plan: free
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: student-management-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: False
      - key: PYTHON_VERSION
        value: 3.11.9
